# AnyDocAI Chat Page Implementation Guide

## Overview

This guide provides a comprehensive implementation strategy for the AnyDocAI chat page, taking into account your existing backend architecture, WebSocket implementation, authentication system, and the need for a clean, maintainable frontend.

## Current Backend Architecture Analysis

### ✅ What's Already Working
- **Authentication System**: Supabase-based auth with JWT tokens and refresh token mechanism
- **WebSocket Manager**: Real-time communication with Socket.IO for streaming responses
- **Chat Service**: Comprehensive chat functionality with session management
- **Document Processing**: LlamaIndex integration for RAG and document processing
- **API Endpoints**: Well-structured REST APIs for chat, sessions, and documents

### 🔧 Key Backend Features to Leverage
- **Streaming Responses**: WebSocket-based real-time chat responses
- **Session Management**: Chat sessions with message history
- **Agent/RAG Toggle**: Switch between agent and RAG modes
- **Document Integration**: Chat with uploaded documents
- **Suggestion System**: AI-generated conversation starters

## Recommended Implementation Strategy

### Phase 1: Core Chat Interface (Immediate Priority)

#### 1.1 Project Setup
```bash
# Create Next.js frontend (if not exists)
npx create-next-app@latest anydocai-frontend --typescript --tailwind --app
cd anydocai-frontend

# Install required dependencies
npm install axios socket.io-client react-markdown react-syntax-highlighter
npm install @types/react-markdown @types/react-syntax-highlighter
npm install lucide-react # For icons
npm install uuid @types/uuid
```

#### 1.2 Directory Structure
```
src/
├── app/
│   ├── page.tsx (Main chat interface)
│   ├── layout.tsx
│   └── globals.css
├── components/
│   ├── chat/
│   │   ├── ChatInterface.tsx
│   │   ├── MessageList.tsx
│   │   ├── MessageInput.tsx
│   │   ├── StreamingMessage.tsx
│   │   └── SuggestedQueries.tsx
│   ├── documents/
│   │   ├── DocumentPanel.tsx
│   │   ├── DocumentUpload.tsx
│   │   └── DocumentList.tsx
│   ├── sessions/
│   │   ├── SessionPanel.tsx
│   │   ├── SessionList.tsx
│   │   └── SessionItem.tsx
│   └── ui/
│       ├── Button.tsx
│       ├── Input.tsx
│       └── LoadingSpinner.tsx
├── services/
│   ├── api.ts
│   ├── websocket.ts
│   └── auth.ts
├── hooks/
│   ├── useChat.ts
│   ├── useWebSocket.ts
│   └── useAuth.ts
├── types/
│   └── index.ts
└── utils/
    └── constants.ts
```

### Phase 2: Core Components Implementation

#### 2.1 API Service Layer
```typescript
// src/services/api.ts
import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

const api = axios.create({
  baseURL: `${API_BASE_URL}/api`,
  withCredentials: true, // Important for cookie-based auth
});

// Request interceptor for auth
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('access_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor for token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      try {
        const refreshResponse = await api.post('/auth/test-refresh-token');
        const newToken = refreshResponse.data.access_token;
        localStorage.setItem('access_token', newToken);

        // Retry original request
        error.config.headers.Authorization = `Bearer ${newToken}`;
        return api.request(error.config);
      } catch (refreshError) {
        // Redirect to login
        window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
);

export const chatApi = {
  // Session management
  createSession: (name: string, documentIds: string[] = []) =>
    api.post('/chat/sessions', { name, document_ids: documentIds }),

  getSessions: () =>
    api.get('/chat/sessions'),

  getSession: (sessionId: string) =>
    api.get(`/chat/sessions/${sessionId}`),

  deleteSession: (sessionId: string) =>
    api.delete(`/chat/sessions/${sessionId}`),

  // Messages
  getMessages: (sessionId: string) =>
    api.get(`/chat/sessions/${sessionId}/messages`),

  sendMessage: (sessionId: string, message: string, useAgent = false) =>
    api.post(`/chat/sessions/${sessionId}/messages`, {
      message,
      use_agent: useAgent
    }),

  // Suggestions
  getSuggestions: (sessionId: string) =>
    api.get(`/chat/sessions/${sessionId}/suggestions`),
};

export const documentApi = {
  upload: (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    return api.post('/documents/upload', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  },

  list: () => api.get('/documents/list'),

  delete: (fileId: string) => api.delete(`/documents/${fileId}`),
};

export default api;
```

#### 2.2 WebSocket Service
```typescript
// src/services/websocket.ts
import { io, Socket } from 'socket.io-client';

class WebSocketService {
  private socket: Socket | null = null;
  private userId: string | null = null;

  connect(userId: string) {
    if (this.socket?.connected) {
      this.disconnect();
    }

    this.userId = userId;
    this.socket = io(process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000', {
      auth: { user_id: userId },
      withCredentials: true,
    });

    this.socket.on('connect', () => {
      console.log('Connected to WebSocket');
    });

    this.socket.on('disconnect', () => {
      console.log('Disconnected from WebSocket');
    });

    return this.socket;
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  joinChatRoom(sessionId: string) {
    this.socket?.emit('join_chat_room', { chat_session_id: sessionId });
  }

  leaveChatRoom(sessionId: string) {
    this.socket?.emit('leave_chat_room', { chat_session_id: sessionId });
  }

  onChatResponse(callback: (data: any) => void) {
    this.socket?.on('chat_response_chunk', callback);
  }

  onFileStatusUpdate(callback: (data: any) => void) {
    this.socket?.on('file_status_update', callback);
  }

  getSocket() {
    return this.socket;
  }
}

export const websocketService = new WebSocketService();
```

#### 2.3 Custom Hooks
```typescript
// src/hooks/useChat.ts
import { useState, useEffect, useCallback } from 'react';
import { chatApi } from '@/services/api';
import { websocketService } from '@/services/websocket';

export interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  isStreaming?: boolean;
}

export interface ChatSession {
  session_id: string;
  name: string;
  created_at: string;
  document_ids: string[];
}

export const useChat = (userId: string) => {
  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const [currentSession, setCurrentSession] = useState<ChatSession | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [streamingMessage, setStreamingMessage] = useState<string>('');
  const [suggestions, setSuggestions] = useState<string[]>([]);

  // Load sessions
  const loadSessions = useCallback(async () => {
    try {
      const response = await chatApi.getSessions();
      setSessions(response.data.sessions || []);
    } catch (error) {
      console.error('Error loading sessions:', error);
    }
  }, []);

  // Create new session
  const createSession = useCallback(async (name?: string, documentIds: string[] = []) => {
    try {
      const sessionName = name || `Chat ${new Date().toLocaleString()}`;
      const response = await chatApi.createSession(sessionName, documentIds);
      const newSession = response.data;

      setSessions(prev => [newSession, ...prev]);
      setCurrentSession(newSession);
      setMessages([]);

      // Load suggestions for new session
      loadSuggestions(newSession.session_id);

      return newSession;
    } catch (error) {
      console.error('Error creating session:', error);
      throw error;
    }
  }, []);

  // Switch to session
  const switchToSession = useCallback(async (session: ChatSession) => {
    try {
      setCurrentSession(session);
      setMessages([]);
      setStreamingMessage('');

      // Leave previous chat room and join new one
      if (currentSession) {
        websocketService.leaveChatRoom(currentSession.session_id);
      }
      websocketService.joinChatRoom(session.session_id);

      // Load messages
      const response = await chatApi.getMessages(session.session_id);
      setMessages(response.data.messages || []);

      // Load suggestions
      loadSuggestions(session.session_id);
    } catch (error) {
      console.error('Error switching session:', error);
    }
  }, [currentSession]);

  // Load suggestions
  const loadSuggestions = useCallback(async (sessionId: string) => {
    try {
      const response = await chatApi.getSuggestions(sessionId);
      setSuggestions(response.data.suggestions || []);
    } catch (error) {
      console.error('Error loading suggestions:', error);
    }
  }, []);

  // Send message
  const sendMessage = useCallback(async (content: string, useAgent = false) => {
    if (!currentSession || !content.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: content.trim(),
      timestamp: new Date().toISOString(),
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);
    setStreamingMessage('');
    setSuggestions([]); // Clear suggestions after first message

    try {
      // Send message via API (this will trigger WebSocket streaming)
      await chatApi.sendMessage(currentSession.session_id, content, useAgent);
    } catch (error) {
      console.error('Error sending message:', error);
      setIsLoading(false);
    }
  }, [currentSession]);

  // Setup WebSocket listeners
  useEffect(() => {
    if (!userId) return;

    websocketService.connect(userId);

    // Handle streaming responses
    websocketService.onChatResponse((data) => {
      if (data.is_final) {
        // Final chunk - add complete message
        const assistantMessage: Message = {
          id: Date.now().toString(),
          role: 'assistant',
          content: streamingMessage + data.chunk,
          timestamp: new Date().toISOString(),
        };

        setMessages(prev => [...prev, assistantMessage]);
        setStreamingMessage('');
        setIsLoading(false);
      } else {
        // Streaming chunk
        setStreamingMessage(prev => prev + data.chunk);
      }
    });

    return () => {
      websocketService.disconnect();
    };
  }, [userId, streamingMessage]);

  // Load initial data
  useEffect(() => {
    if (userId) {
      loadSessions();
    }
  }, [userId, loadSessions]);

  return {
    sessions,
    currentSession,
    messages,
    streamingMessage,
    suggestions,
    isLoading,
    createSession,
    switchToSession,
    sendMessage,
    loadSessions,
  };
};
```

### Phase 3: UI Components

#### 3.1 Main Chat Interface
```typescript
// src/components/chat/ChatInterface.tsx
'use client';

import { useState } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useChat } from '@/hooks/useChat';
import MessageList from './MessageList';
import MessageInput from './MessageInput';
import SuggestedQueries from './SuggestedQueries';
import DocumentPanel from '../documents/DocumentPanel';
import SessionPanel from '../sessions/SessionPanel';

export default function ChatInterface() {
  const { user } = useAuth();
  const [useAgent, setUseAgent] = useState(false);

  const {
    sessions,
    currentSession,
    messages,
    streamingMessage,
    suggestions,
    isLoading,
    createSession,
    switchToSession,
    sendMessage,
  } = useChat(user?.id);

  const handleSendMessage = (content: string) => {
    sendMessage(content, useAgent);
  };

  const handleCreateSession = () => {
    createSession();
  };

  const handleSuggestionClick = (suggestion: string) => {
    sendMessage(suggestion, useAgent);
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Left Panel - Documents */}
      <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
        <DocumentPanel />
      </div>

      {/* Center Panel - Chat */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 p-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-xl font-semibold">
              {currentSession?.name || 'AnyDocAI Chat'}
            </h1>
            {currentSession && (
              <span className="text-sm text-gray-500">
                {currentSession.document_ids.length} documents
              </span>
            )}
          </div>

          <div className="flex items-center space-x-4">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={useAgent}
                onChange={(e) => setUseAgent(e.target.checked)}
                className="rounded border-gray-300"
              />
              <span className="text-sm">Agent Mode</span>
            </label>
          </div>
        </div>

        {/* Chat Area */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {currentSession ? (
            <>
              <MessageList
                messages={messages}
                streamingMessage={streamingMessage}
                isLoading={isLoading}
              />

              {suggestions.length > 0 && messages.length === 0 && (
                <SuggestedQueries
                  suggestions={suggestions}
                  onSuggestionClick={handleSuggestionClick}
                />
              )}

              <MessageInput
                onSendMessage={handleSendMessage}
                disabled={isLoading}
              />
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <h2 className="text-xl font-medium text-gray-900 mb-2">
                  Welcome to AnyDocAI
                </h2>
                <p className="text-gray-600 mb-4">
                  Create a new chat session to get started
                </p>
                <button
                  onClick={handleCreateSession}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
                >
                  Start New Chat
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Right Panel - Sessions */}
      <div className="w-80 bg-white border-l border-gray-200 flex flex-col">
        <SessionPanel
          sessions={sessions}
          currentSession={currentSession}
          onSessionSelect={switchToSession}
          onCreateSession={handleCreateSession}
        />
      </div>
    </div>
  );
}
```

#### 3.2 Key Component Examples

```typescript
// src/components/chat/MessageList.tsx
import { Message } from '@/hooks/useChat';
import ReactMarkdown from 'react-markdown';

interface MessageListProps {
  messages: Message[];
  streamingMessage: string;
  isLoading: boolean;
}

export default function MessageList({ messages, streamingMessage, isLoading }: MessageListProps) {
  return (
    <div className="flex-1 overflow-y-auto p-4 space-y-4">
      {messages.map((message) => (
        <div
          key={message.id}
          className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
        >
          <div
            className={`max-w-3xl p-3 rounded-lg ${
              message.role === 'user'
                ? 'bg-blue-600 text-white'
                : 'bg-white border border-gray-200'
            }`}
          >
            <ReactMarkdown>{message.content}</ReactMarkdown>
            <div className="text-xs opacity-70 mt-1">
              {new Date(message.timestamp).toLocaleTimeString()}
            </div>
          </div>
        </div>
      ))}

      {/* Streaming message */}
      {streamingMessage && (
        <div className="flex justify-start">
          <div className="max-w-3xl p-3 rounded-lg bg-white border border-gray-200">
            <ReactMarkdown>{streamingMessage}</ReactMarkdown>
            <div className="text-xs opacity-70 mt-1">Typing...</div>
          </div>
        </div>
      )}

      {/* Loading indicator */}
      {isLoading && !streamingMessage && (
        <div className="flex justify-start">
          <div className="max-w-3xl p-3 rounded-lg bg-white border border-gray-200">
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <span>AI is thinking...</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
```

```typescript
// src/components/chat/MessageInput.tsx
import { useState, KeyboardEvent } from 'react';

interface MessageInputProps {
  onSendMessage: (message: string) => void;
  disabled: boolean;
}

export default function MessageInput({ onSendMessage, disabled }: MessageInputProps) {
  const [message, setMessage] = useState('');

  const handleSend = () => {
    if (message.trim() && !disabled) {
      onSendMessage(message);
      setMessage('');
    }
  };

  const handleKeyPress = (e: KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <div className="border-t border-gray-200 p-4">
      <div className="flex space-x-4">
        <textarea
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder="Type your message..."
          className="flex-1 resize-none border border-gray-300 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-blue-500"
          rows={3}
          disabled={disabled}
        />
        <button
          onClick={handleSend}
          disabled={disabled || !message.trim()}
          className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Send
        </button>
      </div>
    </div>
  );
}
```

### Phase 4: Integration Points

#### 4.1 Authentication Integration
- Use existing `/auth/test-refresh-token` endpoint for token refresh
- Store tokens in localStorage with automatic refresh
- Implement proper logout flow

#### 4.2 WebSocket Integration
- Connect to existing Socket.IO server on backend
- Handle `chat_response_chunk` events for streaming
- Join/leave chat rooms based on current session

#### 4.3 Document Integration
- Use existing document upload/list APIs
- Associate documents with chat sessions
- Display document context in chat

### Phase 5: Advanced Features

#### 5.1 Chart Rendering
```typescript
// Handle chart data from backend responses
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js';
import { Bar } from 'react-chartjs-2';

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

const renderChart = (chartData: any) => {
  return <Bar data={chartData} options={{ responsive: true }} />;
};
```

#### 5.2 Message Formatting
```typescript
// Support markdown rendering and code syntax highlighting
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { tomorrow } from 'react-syntax-highlighter/dist/esm/styles/prism';

const MarkdownRenderer = ({ content }: { content: string }) => (
  <ReactMarkdown
    components={{
      code({ node, inline, className, children, ...props }) {
        const match = /language-(\w+)/.exec(className || '');
        return !inline && match ? (
          <SyntaxHighlighter
            style={tomorrow}
            language={match[1]}
            PreTag="div"
            {...props}
          >
            {String(children).replace(/\n$/, '')}
          </SyntaxHighlighter>
        ) : (
          <code className={className} {...props}>
            {children}
          </code>
        );
      },
    }}
  >
    {content}
  </ReactMarkdown>
);
```

## Implementation Priority

1. **Phase 1**: Basic chat interface with existing API integration
2. **Phase 2**: WebSocket streaming and real-time updates
3. **Phase 3**: Document integration and session management
4. **Phase 4**: Advanced features (charts, markdown, file attachments)
5. **Phase 5**: Performance optimization and error handling

## Key Considerations

### Security
- Use HTTP-only cookies for refresh tokens
- Implement proper CORS configuration
- Validate all user inputs

### Performance
- Implement message pagination for large chat histories
- Use React.memo for expensive components
- Optimize WebSocket connection management

### User Experience
- Show typing indicators during streaming
- Implement proper loading states
- Handle offline scenarios gracefully

### Error Handling
- Implement retry mechanisms for failed requests
- Show user-friendly error messages
- Handle WebSocket disconnections

## Best Practices

### 1. State Management
- Use React Context for global state
- Keep component state minimal
- Implement proper cleanup in useEffect

### 2. API Integration
- Use proper TypeScript interfaces
- Implement error boundaries
- Handle loading states consistently

### 3. WebSocket Management
- Implement connection retry logic
- Handle reconnection scenarios
- Clean up listeners properly

### 4. Component Architecture
- Keep components focused and reusable
- Use proper prop interfaces
- Implement proper error boundaries

This implementation leverages your existing robust backend architecture while providing a clean, maintainable frontend that can grow with your application's needs. The modular approach allows for incremental development and easy testing of individual components.