# OpenAI API
OPENAI_API_KEY=your_openai_api_key

# Supabase
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key

# Weaviate
WEAVIATE_URL=your_weaviate_url
WEAVIATE_API_KEY=your_weaviate_api_key

# S3 Storage (Wasabi)
S3_ENDPOINT=your_s3_endpoint
S3_ACCESS_KEY=your_s3_access_key
S3_SECRET_KEY=your_s3_secret_key
S3_BUCKET_NAME=your_s3_bucket_name
S3_REGION=your_s3_region

# Redis (for Celery)
REDIS_URL=redis://localhost:6379/0

# App Settings
APP_NAME=AnyDocAI
APP_VERSION=0.1.0
DEBUG=True
ENVIRONMENT=development
SECRET_KEY=your-secret-key-for-jwt-signing

# Authentication Settings
ACCESS_TOKEN_EXPIRE_MINUTES=60
REFRESH_TOKEN_EXPIRE_DAYS=7
TOKEN_BLACKLIST_ENABLED=True

# Rate Limiting Settings
LOGIN_RATE_LIMIT=5
LOGIN_RATE_LIMIT_WINDOW=300
API_RATE_LIMIT=100
API_RATE_LIMIT_WINDOW=60
