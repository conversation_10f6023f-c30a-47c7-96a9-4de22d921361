rolname,query,calls,total_time,prop_total_time
supabase_admin,"LOCK TABLE ""realtime"".""schema_migrations"" IN SHARE UPDATE EXCLUSIVE MODE",160,94817.813476,51.1%
postgres,"with tables as (SELECT
  c.oid :: int8 AS id,
  nc.nspname AS schema,
  c.relname AS name,
  c.relrowsecurity AS rls_enabled,
  c.relforcerowsecurity AS rls_forced,
  CASE
    WHEN c.relreplident = $1 THEN $2
    WHEN c.relreplident = $3 THEN $4
    WHEN c.relreplident = $5 THEN $6
    ELSE $7
  END AS replica_identity,
  pg_total_relation_size(format($8, nc.nspname, c.relname)) :: int8 AS bytes,
  pg_size_pretty(
    pg_total_relation_size(format($9, nc.nspname, c.relname))
  ) AS size,
  pg_stat_get_live_tuples(c.oid) AS live_rows_estimate,
  pg_stat_get_dead_tuples(c.oid) AS dead_rows_estimate,
  obj_description(c.oid) AS comment,
  coalesce(pk.primary_keys, $10) as primary_keys,
  coalesce(
    jsonb_agg(relationships) filter (where relationships is not null),
    $11
  ) as relationships
FROM
  pg_namespace nc
  JOIN pg_class c ON nc.oid = c.relnamespace
  left join (
    select
      table_id,
      jsonb_agg(_pk.*) as primary_keys
    from (
      select
        n.nspname as schema,
        c.relname as table_name,
        a.attname as name,
        c.oid :: int8 as table_id
      from
        pg_index i,
        pg_class c,
        pg_attribute a,
        pg_namespace n
      where
        i.indrelid = c.oid
        and c.relnamespace = n.oid
        and a.attrelid = c.oid
        and a.attnum = any (i.indkey)
        and i.indisprimary
    ) as _pk
    group by table_id
  ) as pk
  on pk.table_id = c.oid
  left join (
    select
      c.oid :: int8 as id,
      c.conname as constraint_name,
      nsa.nspname as source_schema,
      csa.relname as source_table_name,
      sa.attname as source_column_name,
      nta.nspname as target_table_schema,
      cta.relname as target_table_name,
      ta.attname as target_column_name
    from
      pg_constraint c
    join (
      pg_attribute sa
      join pg_class csa on sa.attrelid = csa.oid
      join pg_namespace nsa on csa.relnamespace = nsa.oid
    ) on sa.attrelid = c.conrelid and sa.attnum = any (c.conkey)
    join (
      pg_attribute ta
      join pg_class cta on ta.attrelid = cta.oid
      join pg_namespace nta on cta.relnamespace = nta.oid
    ) on ta.attrelid = c.confrelid and ta.attnum = any (c.confkey)
    where
      c.contype = $12
  ) as relationships
  on (relationships.source_schema = nc.nspname and relationships.source_table_name = c.relname)
  or (relationships.target_table_schema = nc.nspname and relationships.target_table_name = c.relname)
WHERE
  c.relkind IN ($13, $14)
  AND NOT pg_is_other_temp_schema(nc.oid)
  AND (
    pg_has_role(c.relowner, $15)
    OR has_table_privilege(
      c.oid,
      $16
    )
    OR has_any_column_privilege(c.oid, $17)
  )
group by
  c.oid,
  c.relname,
  c.relrowsecurity,
  c.relforcerowsecurity,
  c.relreplident,
  nc.nspname,
  pk.primary_keys
)
  , columns as (-- Adapted from information_schema.columns

SELECT
  c.oid :: int8 AS table_id,
  nc.nspname AS schema,
  c.relname AS table,
  (c.oid || $18 || a.attnum) AS id,
  a.attnum AS ordinal_position,
  a.attname AS name,
  CASE
    WHEN a.atthasdef THEN pg_get_expr(ad.adbin, ad.adrelid)
    ELSE $19
  END AS default_value,
  CASE
    WHEN t.typtype = $20 THEN CASE
      WHEN bt.typelem <> $21 :: oid
      AND bt.typlen = $22 THEN $23
      WHEN nbt.nspname = $24 THEN format_type(t.typbasetype, $25)
      ELSE $26
    END
    ELSE CASE
      WHEN t.typelem <> $27 :: oid
      AND t.typlen = $28 THEN $29
      WHEN nt.nspname = $30 THEN format_type(a.atttypid, $31)
      ELSE $32
    END
  END AS data_type,
  COALESCE(bt.typname, t.typname) AS format,
  a.attidentity IN ($33, $34) AS is_identity,
  CASE
    a.attidentity
    WHEN $35 THEN $36
    WHEN $37 THEN $38
    ELSE $39
  END AS identity_generation,
  a.attgenerated IN ($40) AS is_generated,
  NOT (
    a.attnotnull
    OR t.typtype = $41 AND t.typnotnull
  ) AS is_nullable,
  (
    c.relkind IN ($42, $43)
    OR c.relkind IN ($44, $45) AND pg_column_is_updatable(c.oid, a.attnum, $46)
  ) AS is_updatable,
  uniques.table_id IS NOT NULL AS is_unique,
  check_constraints.definition AS ""check"",
  array_to_json(
    array(
      SELECT
        enumlabel
      FROM
        pg_catalog.pg_enum enums
      WHERE
        enums.enumtypid = coalesce(bt.oid, t.oid)
        OR enums.enumtypid = coalesce(bt.typelem, t.typelem)
      ORDER BY
        enums.enumsortorder
    )
  ) AS enums,
  col_description(c.oid, a.attnum) AS comment
FROM
  pg_attribute a
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid
  AND a.attnum = ad.adnum
  JOIN (
    pg_class c
    JOIN pg_namespace nc ON c.relnamespace = nc.oid
  ) ON a.attrelid = c.oid
  JOIN (
    pg_type t
    JOIN pg_namespace nt ON t.typnamespace = nt.oid
  ) ON a.atttypid = t.oid
  LEFT JOIN (
    pg_type bt
    JOIN pg_namespace nbt ON bt.typnamespace = nbt.oid
  ) ON t.typtype = $47
  AND t.typbasetype = bt.oid
  LEFT JOIN (
    SELECT DISTINCT ON (table_id, ordinal_position)
      conrelid AS table_id,
      conkey[$48] AS ordinal_position
    FROM pg_catalog.pg_constraint
    WHERE contype = $49 AND cardinality(conkey) = $50
  ) AS uniques ON uniques.table_id = c.oid AND uniques.ordinal_position = a.attnum
  LEFT JOIN (
    -- We only select the first column check
    SELECT DISTINCT ON (table_id, ordinal_position)
      conrelid AS table_id,
      conkey[$51] AS ordinal_position,
      substring(
        pg_get_constraintdef(pg_constraint.oid, $52),
        $53,
        length(pg_get_constraintdef(pg_constraint.oid, $54)) - $55
      ) AS ""definition""
    FROM pg_constraint
    WHERE contype = $56 AND cardinality(conkey) = $57
    ORDER BY table_id, ordinal_position, oid asc
  ) AS check_constraints ON check_constraints.table_id = c.oid AND check_constraints.ordinal_position = a.attnum
WHERE
  NOT pg_is_other_temp_schema(nc.oid)
  AND a.attnum > $58
  AND NOT a.attisdropped
  AND (c.relkind IN ($59, $60, $61, $62, $63))
  AND (
    pg_has_role(c.relowner, $64)
    OR has_column_privilege(
      c.oid,
      a.attnum,
      $65
    )
  )
)
select
  *
  , 
COALESCE(
  (
    SELECT
      array_agg(row_to_json(columns)) FILTER (WHERE columns.table_id = tables.id)
    FROM
      columns
  ),
  $66
) AS columns
from tables where schema IN ($67)",266,23050.082143,12.4%
authenticator,SELECT name FROM pg_timezone_names,85,15630.523714,8.4%
postgres,"with f as (
      
-- CTE with sane arg_modes, arg_names, and arg_types.
-- All three are always of the same length.
-- All three include all args, including OUT and TABLE args.
with functions as (
  select
    *,
    -- proargmodes is null when all arg modes are IN
    coalesce(
      p.proargmodes,
      array_fill($1::text, array[cardinality(coalesce(p.proallargtypes, p.proargtypes))])
    ) as arg_modes,
    -- proargnames is null when all args are unnamed
    coalesce(
      p.proargnames,
      array_fill($2::text, array[cardinality(coalesce(p.proallargtypes, p.proargtypes))])
    ) as arg_names,
    -- proallargtypes is null when all arg modes are IN
    coalesce(p.proallargtypes, p.proargtypes) as arg_types,
    array_cat(
      array_fill($3, array[pronargs - pronargdefaults]),
      array_fill($4, array[pronargdefaults])) as arg_has_defaults
  from
    pg_proc as p
  where
    p.prokind = $5
)
select
  f.oid as id,
  n.nspname as schema,
  f.proname as name,
  l.lanname as language,
  case
    when l.lanname = $6 then $7
    else f.prosrc
  end as definition,
  case
    when l.lanname = $8 then f.prosrc
    else pg_get_functiondef(f.oid)
  end as complete_statement,
  coalesce(f_args.args, $9) as args,
  pg_get_function_arguments(f.oid) as argument_types,
  pg_get_function_identity_arguments(f.oid) as identity_argument_types,
  f.prorettype as return_type_id,
  pg_get_function_result(f.oid) as return_type,
  nullif(rt.typrelid, $10) as return_type_relation_id,
  f.proretset as is_set_returning_function,
  case
    when f.provolatile = $11 then $12
    when f.provolatile = $13 then $14
    when f.provolatile = $15 then $16
  end as behavior,
  f.prosecdef as security_definer,
  f_config.config_params as config_params
from
  functions f
  left join pg_namespace n on f.pronamespace = n.oid
  left join pg_language l on f.prolang = l.oid
  left join pg_type rt on rt.oid = f.prorettype
  left join (
    select
      oid,
      jsonb_object_agg(param, value) filter (where param is not null) as config_params
    from
      (
        select
          oid,
          (string_to_array(unnest(proconfig), $17))[$18] as param,
          (string_to_array(unnest(proconfig), $19))[$20] as value
        from
          functions
      ) as t
    group by
      oid
  ) f_config on f_config.oid = f.oid
  left join (
    select
      oid,
      jsonb_agg(jsonb_build_object(
        $21, t2.mode,
        $22, name,
        $23, type_id,
        -- Cast null into false boolean
        $24, COALESCE(has_default, $25)
      )) as args
    from
      (
        select
          oid,
          unnest(arg_modes) as mode,
          unnest(arg_names) as name,
          -- Coming from: coalesce(p.proallargtypes, p.proargtypes) postgres won't automatically assume
          -- integer, we need to cast it to be properly parsed
          unnest(arg_types)::int8 as type_id,
          unnest(arg_has_defaults) as has_default
        from
          functions
      ) as t1,
      lateral (
        select
          case
            when t1.mode = $26 then $27
            when t1.mode = $28 then $29
            when t1.mode = $30 then $31
            when t1.mode = $32 then $33
            else $34
          end as mode
      ) as t2
    group by
      t1.oid
  ) f_args on f_args.oid = f.oid

    )
    select
      f.*
    from f
   where schema NOT IN ($35,$36,$37)

-- source: dashboard
-- user: bae399e7-f35d-456c-8c0a-77d41e171e1b
-- date: 2025-04-20T06:16:49.251Z",53,15006.275003,8.1%
postgres,"with base_table_info as ( select c.oid::int8 as id, nc.nspname as schema, c.relname as name, c.relkind, c.relrowsecurity as rls_enabled, c.relforcerowsecurity as rls_forced, c.relreplident, c.relowner, obj_description(c.oid) as comment from pg_class c join pg_namespace nc on nc.oid = c.relnamespace where c.oid = $1 and not pg_is_other_temp_schema(nc.oid) and ( pg_has_role(c.relowner, $2) or has_table_privilege( c.oid, $3 ) or has_any_column_privilege(c.oid, $4) ) ), table_stats as ( select b.id, case when b.relreplident = $5 then $6 when b.relreplident = $7 then $8 when b.relreplident = $9 then $10 else $11 end as replica_identity, pg_total_relation_size(format($12, b.schema, b.name))::int8 as bytes, pg_size_pretty(pg_total_relation_size(format($13, b.schema, b.name))) as size, pg_stat_get_live_tuples(b.id) as live_rows_estimate, pg_stat_get_dead_tuples(b.id) as dead_rows_estimate from base_table_info b where b.relkind in ($14, $15) ), primary_keys as ( select i.indrelid as table_id, jsonb_agg(jsonb_build_object( $16, n.nspname, $17, c.relname, $18, i.indrelid::int8, $19, a.attname )) as primary_keys from pg_index i join pg_class c on i.indrelid = c.oid join pg_attribute a on (a.attrelid = c.oid and a.attnum = any(i.indkey)) join pg_namespace n on c.relnamespace = n.oid where i.indisprimary group by i.indrelid ), relationships as ( select c.conrelid as source_id, c.confrelid as target_id, jsonb_build_object( $20, c.oid::int8, $21, c.conname, $22, c.confdeltype, $23, c.confupdtype, $24, nsa.nspname, $25, csa.relname, $26, sa.attname, $27, nta.nspname, $28, cta.relname, $29, ta.attname ) as rel_info from pg_constraint c join pg_class csa on c.conrelid = csa.oid join pg_namespace nsa on csa.relnamespace = nsa.oid join pg_attribute sa on (sa.attrelid = c.conrelid and sa.attnum = any(c.conkey)) join pg_class cta on c.confrelid = cta.oid join pg_namespace nta on cta.relnamespace = nta.oid join pg_attribute ta on (ta.attrelid = c.confrelid and ta.attnum = any(c.confkey)) where c.contype = $30 ), columns as ( select a.attrelid as table_id, jsonb_agg(jsonb_build_object( $31, (a.attrelid || $32 || a.attnum), $33, c.oid::int8, $34, nc.nspname, $35, c.relname, $36, a.attnum, $37, a.attname, $38, case when a.atthasdef then pg_get_expr(ad.adbin, ad.adrelid) else $39 end, $40, case when t.typtype = $41 then case when bt.typelem <> $42::oid and bt.typlen = $43 then $44 when nbt.nspname = $45 then format_type(t.typbasetype, $46) else $47 end else case when t.typelem <> $48::oid and t.typlen = $49 then $50 when nt.nspname = $51 then format_type(a.atttypid, $52) else $53 end end, $54, case when t.typtype = $55 then case when nt.nspname <> $56 then concat(nt.nspname, $57, coalesce(bt.typname, t.typname)) else coalesce(bt.typname, t.typname) end else coalesce(bt.typname, t.typname) end, $58, a.attidentity in ($59, $60), $61, case a.attidentity when $62 then $63 when $64 then $65 else $66 end, $67, a.attgenerated in ($68), $69, not (a.attnotnull or t.typtype = $70 and t.typnotnull), $71, ( b.relkind in ($72, $73) or (b.relkind in ($74, $75) and pg_column_is_updatable(b.id, a.attnum, $76)) ), $77, uniques.table_id is not null, $78, check_constraints.definition, $79, col_description(c.oid, a.attnum), $80, coalesce( ( select jsonb_agg(e.enumlabel order by e.enumsortorder) from pg_catalog.pg_enum e where e.enumtypid = coalesce(bt.oid, t.oid) or e.enumtypid = coalesce(bt.typelem, t.typelem) ), $81::jsonb ) ) order by a.attnum) as columns from pg_attribute a join base_table_info b on a.attrelid = b.id join pg_class c on a.attrelid = c.oid join pg_namespace nc on c.relnamespace = nc.oid left join pg_attrdef ad on (a.attrelid = ad.adrelid and a.attnum = ad.adnum) join pg_type t on a.atttypid = t.oid join pg_namespace nt on t.typnamespace = nt.oid left join pg_type bt on (t.typtype = $82 and t.typbasetype = bt.oid) left join pg_namespace nbt on bt.typnamespace = nbt.oid left join ( select conrelid as table_id, conkey[$83] as ordinal_position from pg_catalog.pg_constraint where contype = $84 and cardinality(conkey) = $85 group by conrelid, conkey[1] ) as uniques on uniques.table_id = a.attrelid and uniques.ordinal_position = a.attnum left join ( select distinct on (conrelid, conkey[1]) conrelid as table_id, conkey[$86] as ordinal_position, substring( pg_get_constraintdef(oid, $87), $88, length(pg_get_constraintdef(oid, $89)) - $90 ) as definition from pg_constraint where contype = $91 and cardinality(conkey) = $92 order by conrelid, conkey[1], oid asc ) as check_constraints on check_constraints.table_id = a.attrelid and check_constraints.ordinal_position = a.attnum where a.attnum > $93 and not a.attisdropped group by a.attrelid ) select case b.relkind when $94 then jsonb_build_object( $95, b.relkind, $96, b.id, $97, b.schema, $98, b.name, $99, b.rls_enabled, $100, b.rls_forced, $101, ts.replica_identity, $102, ts.bytes, $103, ts.size, $104, ts.live_rows_estimate, $105, ts.dead_rows_estimate, $106, b.comment, $107, coalesce(pk.primary_keys, $108::jsonb), $109, coalesce( (select jsonb_agg(r.rel_info) from relationships r where r.source_id = b.id or r.target_id = b.id), $110::jsonb ), $111, coalesce(c.columns, $112::jsonb) ) when $113 then jsonb_build_object( $114, b.relkind, $115, b.id, $116, b.schema, $117, b.name, $118, b.rls_enabled, $119, b.rls_forced, $120, ts.replica_identity, $121, ts.bytes, $122, ts.size, $123, ts.live_rows_estimate, $124, ts.dead_rows_estimate, $125, b.comment, $126, coalesce(pk.primary_keys, $127::jsonb), $128, coalesce( (select jsonb_agg(r.rel_info) from relationships r where r.source_id = b.id or r.target_id = b.id), $129::jsonb ), $130, coalesce(c.columns, $131::jsonb) ) when $132 then jsonb_build_object( $133, b.relkind, $134, b.id, $135, b.schema, $136, b.name, $137, (pg_relation_is_updatable(b.id, $138) & $139) = $140, $141, b.comment, $142, coalesce(c.columns, $143::jsonb) ) when $144 then jsonb_build_object( $145, b.relkind, $146, b.id, $147, b.schema, $148, b.name, $149, $150, $151, b.comment, $152, coalesce(c.columns, $153::jsonb) ) when $154 then jsonb_build_object( $155, b.relkind, $156, b.id, $157, b.schema, $158, b.name, $159, b.comment, $160, coalesce(c.columns, $161::jsonb) ) end as entity from base_table_info b left join table_stats ts on b.id = ts.id left join primary_keys pk on b.id = pk.table_id left join columns c on b.id = c.table_id",542,3718.707128,2.0%
postgres,"with tables as (SELECT
  c.oid :: int8 AS id,
  nc.nspname AS schema,
  c.relname AS name,
  c.relrowsecurity AS rls_enabled,
  c.relforcerowsecurity AS rls_forced,
  CASE
    WHEN c.relreplident = $1 THEN $2
    WHEN c.relreplident = $3 THEN $4
    WHEN c.relreplident = $5 THEN $6
    ELSE $7
  END AS replica_identity,
  pg_total_relation_size(format($8, nc.nspname, c.relname)) :: int8 AS bytes,
  pg_size_pretty(
    pg_total_relation_size(format($9, nc.nspname, c.relname))
  ) AS size,
  pg_stat_get_live_tuples(c.oid) AS live_rows_estimate,
  pg_stat_get_dead_tuples(c.oid) AS dead_rows_estimate,
  obj_description(c.oid) AS comment,
  coalesce(pk.primary_keys, $10) as primary_keys,
  coalesce(
    jsonb_agg(relationships) filter (where relationships is not null),
    $11
  ) as relationships
FROM
  pg_namespace nc
  JOIN pg_class c ON nc.oid = c.relnamespace
  left join (
    select
      table_id,
      jsonb_agg(_pk.*) as primary_keys
    from (
      select
        n.nspname as schema,
        c.relname as table_name,
        a.attname as name,
        c.oid :: int8 as table_id
      from
        pg_index i,
        pg_class c,
        pg_attribute a,
        pg_namespace n
      where
        i.indrelid = c.oid
        and c.relnamespace = n.oid
        and a.attrelid = c.oid
        and a.attnum = any (i.indkey)
        and i.indisprimary
    ) as _pk
    group by table_id
  ) as pk
  on pk.table_id = c.oid
  left join (
    select
      c.oid :: int8 as id,
      c.conname as constraint_name,
      nsa.nspname as source_schema,
      csa.relname as source_table_name,
      sa.attname as source_column_name,
      nta.nspname as target_table_schema,
      cta.relname as target_table_name,
      ta.attname as target_column_name
    from
      pg_constraint c
    join (
      pg_attribute sa
      join pg_class csa on sa.attrelid = csa.oid
      join pg_namespace nsa on csa.relnamespace = nsa.oid
    ) on sa.attrelid = c.conrelid and sa.attnum = any (c.conkey)
    join (
      pg_attribute ta
      join pg_class cta on ta.attrelid = cta.oid
      join pg_namespace nta on cta.relnamespace = nta.oid
    ) on ta.attrelid = c.confrelid and ta.attnum = any (c.confkey)
    where
      c.contype = $12
  ) as relationships
  on (relationships.source_schema = nc.nspname and relationships.source_table_name = c.relname)
  or (relationships.target_table_schema = nc.nspname and relationships.target_table_name = c.relname)
WHERE
  c.relkind IN ($13, $14)
  AND NOT pg_is_other_temp_schema(nc.oid)
  AND (
    pg_has_role(c.relowner, $15)
    OR has_table_privilege(
      c.oid,
      $16
    )
    OR has_any_column_privilege(c.oid, $17)
  )
group by
  c.oid,
  c.relname,
  c.relrowsecurity,
  c.relforcerowsecurity,
  c.relreplident,
  nc.nspname,
  pk.primary_keys
)
  
select
  *
  
from tables where schema IN ($18)",94,2202.700788,1.2%
authenticator,"-- Recursively get the base types of domains
  WITH
  base_types AS (
    WITH RECURSIVE
    recurse AS (
      SELECT
        oid,
        typbasetype,
        COALESCE(NULLIF(typbasetype, $3), oid) AS base
      FROM pg_type
      UNION
      SELECT
        t.oid,
        b.typbasetype,
        COALESCE(NULLIF(b.typbasetype, $4), b.oid) AS base
      FROM recurse t
      JOIN pg_type b ON t.typbasetype = b.oid
    )
    SELECT
      oid,
      base
    FROM recurse
    WHERE typbasetype = $5
  ),
  arguments AS (
    SELECT
      oid,
      array_agg((
        COALESCE(name, $6), -- name
        type::regtype::text, -- type
        CASE type
          WHEN $7::regtype THEN $8
          WHEN $9::regtype THEN $10
          WHEN $11::regtype THEN $12
          WHEN $13::regtype THEN $14
          ELSE type::regtype::text
        END, -- convert types that ignore the lenth and accept any value till maximum size
        idx <= (pronargs - pronargdefaults), -- is_required
        COALESCE(mode = $15, $16) -- is_variadic
      ) ORDER BY idx) AS args,
      CASE COUNT(*) - COUNT(name) -- number of unnamed arguments
        WHEN $17 THEN $18
        WHEN $19 THEN (array_agg(type))[$20] IN ($21::regtype, $22::regtype, $23::regtype, $24::regtype, $25::regtype)
        ELSE $26
      END AS callable
    FROM pg_proc,
         unnest(proargnames, proargtypes, proargmodes)
           WITH ORDINALITY AS _ (name, type, mode, idx)
    WHERE type IS NOT NULL -- only input arguments
    GROUP BY oid
  )
  SELECT
    pn.nspname AS proc_schema,
    p.proname AS proc_name,
    d.description AS proc_description,
    COALESCE(a.args, $27) AS args,
    tn.nspname AS schema,
    COALESCE(comp.relname, t.typname) AS name,
    p.proretset AS rettype_is_setof,
    (t.typtype = $28
     -- if any TABLE, INOUT or OUT arguments present, treat as composite
     or COALESCE(proargmodes::text[] && $29, $30)
    ) AS rettype_is_composite,
    bt.oid <> bt.base as rettype_is_composite_alias,
    p.provolatile,
    p.provariadic > $31 as hasvariadic,
    lower((regexp_split_to_array((regexp_split_to_array(iso_config, $32))[$33], $34))[$35]) AS transaction_isolation_level,
    coalesce(func_settings.kvs, $36) as kvs
  FROM pg_proc p
  LEFT JOIN arguments a ON a.oid = p.oid
  JOIN pg_namespace pn ON pn.oid = p.pronamespace
  JOIN base_types bt ON bt.oid = p.prorettype
  JOIN pg_type t ON t.oid = bt.base
  JOIN pg_namespace tn ON tn.oid = t.typnamespace
  LEFT JOIN pg_class comp ON comp.oid = t.typrelid
  LEFT JOIN pg_description as d ON d.objoid = p.oid
  LEFT JOIN LATERAL unnest(proconfig) iso_config ON iso_config LIKE $37
  LEFT JOIN LATERAL (
    SELECT
      array_agg(row(
        substr(setting, $38, strpos(setting, $39) - $40),
        substr(setting, strpos(setting, $41) + $42)
      )) as kvs
    FROM unnest(proconfig) setting
    WHERE setting ~ ANY($2)
  ) func_settings ON $43
  WHERE t.oid <> $44::regtype AND COALESCE(a.callable, $45)
AND prokind = $46 AND pn.nspname = ANY($1)",85,1936.831457,1.0%
supabase_admin,"CREATE TABLE IF NOT EXISTS ""realtime"".""schema_migrations"" (""version"" bigint, ""inserted_at"" timestamp(0), PRIMARY KEY (""version""))",46,1840.625299,1.0%
postgres,"SELECT
  e.name,
  n.nspname AS schema,
  e.default_version,
  x.extversion AS installed_version,
  e.comment
FROM
  pg_available_extensions() e(name, default_version, comment)
  LEFT JOIN pg_extension x ON e.name = x.extname
  LEFT JOIN pg_namespace n ON x.extnamespace = n.oid",23,1626.104499,0.9%
supabase_auth_admin,"INSERT INTO ""refresh_tokens"" (""created_at"", ""instance_id"", ""parent"", ""revoked"", ""session_id"", ""token"", ""updated_at"", ""user_id"") VALUES ($1, $2, $3, $4, $5, $6, $7, $8) returning id",265,1246.016018,0.7%
supabase_auth_admin,"INSERT INTO ""sessions"" (""aal"", ""created_at"", ""factor_id"", ""id"", ""ip"", ""not_after"", ""refreshed_at"", ""tag"", ""updated_at"", ""user_agent"", ""user_id"") VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)",214,748.975957000001,0.4%
postgres,"select
  t.oid::int8 as id,
  t.typname as name,
  n.nspname as schema,
  format_type (t.oid, $1) as format,
  coalesce(t_enums.enums, $2) as enums,
  coalesce(t_attributes.attributes, $3) as attributes,
  obj_description (t.oid, $4) as comment
from
  pg_type t
  left join pg_namespace n on n.oid = t.typnamespace
  left join (
    select
      enumtypid,
      jsonb_agg(enumlabel order by enumsortorder) as enums
    from
      pg_enum
    group by
      enumtypid
  ) as t_enums on t_enums.enumtypid = t.oid
  left join (
    select
      oid,
      jsonb_agg(
        jsonb_build_object($5, a.attname, $6, a.atttypid::int8)
        order by a.attnum asc
      ) as attributes
    from
      pg_class c
      join pg_attribute a on a.attrelid = c.oid
    where
      c.relkind = $7 and not a.attisdropped
    group by
      c.oid
  ) as t_attributes on t_attributes.oid = t.typrelid

    where
      (
        t.typrelid = $8
        or (
          select
            c.relkind = $9
          from
            pg_class c
          where
            c.oid = t.typrelid
        )
      )
     and not exists (
                 select
                 from
                   pg_type el
                 where
                   el.oid = t.typelem
                   and el.typarray = t.oid
               ) and n.nspname NOT IN ($10,$11,$12)",309,705.072486,0.4%
supabase_auth_admin,"INSERT INTO ""audit_log_entries"" (""created_at"", ""id"", ""instance_id"", ""ip_address"", ""payload"") VALUES ($1, $2, $3, $4, $5)",340,632.018845,0.3%
supabase_admin,"SELECT t.oid, t.typname, t.typsend, t.typreceive, t.typoutput, t.typinput,
       coalesce(d.typelem, t.typelem), coalesce(r.rngsubtype, $1), ARRAY (
  SELECT a.atttypid
  FROM pg_attribute AS a
  WHERE a.attrelid = t.typrelid AND a.attnum > $2 AND NOT a.attisdropped
  ORDER BY a.attnum
)

FROM pg_type AS t
LEFT JOIN pg_type AS d ON t.typbasetype = d.oid
LEFT JOIN pg_range AS r ON r.rngtypid = t.oid OR r.rngmultitypid = t.oid OR (t.typbasetype <> $3 AND r.rngtypid = t.typbasetype)
WHERE (t.typrelid = $4)
AND (t.typelem = $5 OR NOT EXISTS (SELECT $6 FROM pg_catalog.pg_type s WHERE s.typrelid != $7 AND s.oid = t.typelem))",61,586.538894,0.3%
authenticator,"SELECT current_setting($1)::integer, current_setting($2), version()",170,562.519845,0.3%
supabase_auth_admin,"SELECT users.aud, users.banned_until, users.confirmation_sent_at, users.confirmation_token, users.confirmed_at, users.created_at, users.deleted_at, users.email, users.email_change, users.email_change_confirm_status, users.email_change_sent_at, users.email_change_token_current, users.email_change_token_new, users.email_confirmed_at, users.encrypted_password, users.id, users.instance_id, users.invited_at, users.is_anonymous, users.is_sso_user, users.last_sign_in_at, users.phone, users.phone_change, users.phone_change_sent_at, users.phone_change_token, users.phone_confirmed_at, users.raw_app_meta_data, users.raw_user_meta_data, users.reauthentication_sent_at, users.reauthentication_token, users.recovery_sent_at, users.recovery_token, users.role, users.updated_at FROM users AS users WHERE instance_id = $1 and LOWER(email) = $2 and aud = $3 and is_sso_user = $4 LIMIT $5",254,530.348424,0.3%
postgres,"with records as (
  select
    c.oid::int8 as ""id"",
    case c.relkind
      when $1 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $2,
        $3,
        $4
      )
      when $5 then concat(
        $6, concat(nc.nspname, $7, c.relname), $8,
        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)
      )
      when $11 then concat(
        $12, concat(nc.nspname, $13, c.relname), $14,
        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)
      )
      when $17 then concat($18, nc.nspname, $19, c.relname, $20)
      when $21 then pg_temp.pg_get_tabledef(
        concat(nc.nspname),
        concat(c.relname),
        $22,
        $23,
        $24
      )
    end as ""sql""
  from
    pg_namespace nc
    join pg_class c on nc.oid = c.relnamespace
  where
    c.relkind in ($25, $26, $27, $28, $29)
    and not pg_is_other_temp_schema(nc.oid)
    and (
      pg_has_role(c.relowner, $30)
      or has_table_privilege(
        c.oid,
        $31
      )
      or has_any_column_privilege(c.oid, $32)
    )
    and nc.nspname IN ($33)
  order by c.relname asc
  limit $34
  offset $35
)
select
  jsonb_build_object(
    $36, coalesce(jsonb_agg(
      jsonb_build_object(
        $37, r.id,
        $38, r.sql
      )
    ), $39::jsonb)
  ) ""data""
from records r",1,519.8631,0.3%
service_role,"WITH pgrst_source AS (UPDATE ""public"".""users"" SET ""last_login"" = ""pgrst_body"".""last_login"" FROM (SELECT $1 AS json_data) pgrst_payload, LATERAL (SELECT ""last_login"" FROM json_to_record(pgrst_payload.json_data) AS _(""last_login"" timestamp with time zone) ) pgrst_body  WHERE  ""public"".""users"".""id"" = $2 RETURNING ""public"".""users"".*) SELECT $3 AS total_result_set, pg_catalog.count(_postgrest_t) AS page_total, array[]::text[] AS header, coalesce(json_agg(_postgrest_t), $4) AS body, nullif(current_setting($5, $6), $7) AS response_headers, nullif(current_setting($8, $9), $10) AS response_status, $11 AS response_inserted FROM (SELECT ""users"".* FROM ""pgrst_source"" AS ""users""     ) _postgrest_t",2844,494.2079,0.3%
authenticated,"select set_config('search_path', $1, true), set_config($2, $3, true), set_config('role', $4, true), set_config('request.jwt.claims', $5, true), set_config('request.method', $6, true), set_config('request.path', $7, true), set_config('request.headers', $8, true), set_config('request.cookies', $9, true)",3983,490.529614999999,0.3%
authenticator,"with
      role_setting as (
        select r.rolname, unnest(r.rolconfig) as setting
        from pg_auth_members m
        join pg_roles r on r.oid = m.roleid
        where member = current_user::regrole::oid
      ),
      kv_settings AS (
        SELECT
          rolname,
          substr(setting, $1, strpos(setting, $2) - $3) as key,
          lower(substr(setting, strpos(setting, $4) + $5)) as value
        FROM role_setting
      ),
      iso_setting AS (
        SELECT rolname, value
        FROM kv_settings
        WHERE key = $6
      )
      select
        kv.rolname,
        i.value as iso_lvl,
        coalesce(array_agg(row(kv.key, kv.value)) filter (where key <> $7), $8) as role_settings
      from kv_settings kv
      join pg_settings ps on ps.name = kv.key and (ps.context = $9 or has_parameter_privilege(current_user::regrole::oid, ps.name, $10)) 
      left join iso_setting i on i.rolname = kv.rolname
      group by kv.rolname, i.value",85,440.329683,0.2%